<?php
/**
 * WooCommerce Order Completion Handler for Custom Linking Plugin
 * 
 * This file handles WooCommerce order completion events and automatically
 * grants course access to users who purchase linked products
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Initialize WooCommerce order completion hooks
 * 
 * This function sets up hooks for both processing and completed order statuses
 * to handle different payment methods (offline payments go to processing)
 */
function custom_linking_init_order_hooks() {
    // Hook into order status changes for both processing and completed
    add_action('woocommerce_order_status_processing', 'custom_linking_handle_order_completion');
    add_action('woocommerce_order_status_completed', 'custom_linking_handle_order_completion');
    
    // Also hook into order payment complete for immediate processing
    add_action('woocommerce_payment_complete', 'custom_linking_handle_order_completion');
}

/**
 * Handle WooCommerce order completion
 *
 * This function processes completed/processing orders and grants course access
 * for any linked products in the order
 *
 * @param int $order_id The WooCommerce order ID
 */
function custom_linking_handle_order_completion($order_id) {
    try {
        // Validate order ID
        if (!$order_id) {
            custom_linking_debug_log('Order completion: No order ID provided');
            return;
        }

        // Check if WooCommerce is available
        if (!function_exists('wc_get_order')) {
            custom_linking_debug_log('Order completion: WooCommerce functions not available');
            return;
        }

        // Get the WooCommerce order object
        $order = wc_get_order($order_id);
        if (!$order) {
            custom_linking_debug_log("Order completion: Could not retrieve order {$order_id}");
            return;
        }

        // Get the user who made the purchase
        $user_id = $order->get_user_id();
        if (!$user_id) {
            custom_linking_debug_log("Order completion: No user ID found for order {$order_id}");
            return;
        }

        // Log the order processing start
        custom_linking_debug_log("Processing order {$order_id} for user {$user_id}");

        // Get all items in the order
        $order_items = $order->get_items();
        if (empty($order_items)) {
            custom_linking_debug_log("Order completion: No items found in order {$order_id}");
            return;
        }

        // Process each item in the order
        foreach ($order_items as $item_id => $item) {
            try {
                $product_id = $item->get_product_id();

                if ($product_id) {
                    custom_linking_debug_log("Processing product {$product_id} from order {$order_id}");

                    // Find linked courses for this product
                    $linked_courses = custom_linking_get_courses_for_product($product_id);

                    if (!empty($linked_courses)) {
                        foreach ($linked_courses as $course_link) {
                            try {
                                $course_id = $course_link->course_id;
                                $link_type = $course_link->type;

                                custom_linking_debug_log("Found linked course {$course_id} (type: {$link_type}) for product {$product_id}");

                                // For now, just log that we would grant access (to avoid errors)
                                custom_linking_debug_log("WOULD GRANT: Course {$course_id} access to user {$user_id} (type: {$link_type})");

                                // TODO: Implement actual course access granting after testing
                                // $access_granted = custom_linking_grant_course_access($user_id, $course_id, $link_type);
                            } catch (Exception $e) {
                                custom_linking_debug_log("Exception processing course link: " . $e->getMessage());
                            }
                        }
                    } else {
                        custom_linking_debug_log("No linked courses found for product {$product_id}");
                    }
                } else {
                    custom_linking_debug_log("No product ID found for item in order {$order_id}");
                }
            } catch (Exception $e) {
                custom_linking_debug_log("Exception processing order item: " . $e->getMessage());
            }
        }

        custom_linking_debug_log("Completed processing order {$order_id}");

    } catch (Exception $e) {
        custom_linking_debug_log("Critical exception in order completion: " . $e->getMessage());
    }
}

/**
 * Get linked courses for a specific product ID
 * 
 * @param int $product_id The WooCommerce product ID
 * @return array Array of course link objects
 */
function custom_linking_get_courses_for_product($product_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $results = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT course_id, type FROM $table_name WHERE product_id = %d",
            $product_id
        )
    );
    
    return $results ?: array();
}

/**
 * Grant course access to a user
 *
 * This function uses MasterStudy LMS functions to grant course access
 *
 * @param int $user_id The WordPress user ID
 * @param int $course_id The course ID
 * @param string $link_type The type of link (course or bundle)
 * @return bool True if access was granted successfully
 */
function custom_linking_grant_course_access($user_id, $course_id, $link_type = 'course') {
    try {
        custom_linking_debug_log("Starting course access grant for user {$user_id}, course {$course_id}");

        // Check if course exists
        $course = get_post($course_id);
        if (!$course || $course->post_type !== 'stm-courses') {
            custom_linking_debug_log("Course {$course_id} not found or not a valid course");
            return false;
        }

        // Check if user exists
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            custom_linking_debug_log("User {$user_id} not found");
            return false;
        }

        // Try different MasterStudy LMS enrollment methods
        $access_granted = false;

        // Method 1: Try using STM LMS enrollment function (common pattern)
        if (function_exists('stm_lms_add_user_course')) {
            try {
                $result = stm_lms_add_user_course($user_id, $course_id);
                if ($result) {
                    $access_granted = true;
                    custom_linking_debug_log("Method 1: stm_lms_add_user_course succeeded for user {$user_id}, course {$course_id}");
                } else {
                    custom_linking_debug_log("Method 1: stm_lms_add_user_course failed for user {$user_id}, course {$course_id}");
                }
            } catch (Exception $e) {
                custom_linking_debug_log("Method 1: Exception in stm_lms_add_user_course: " . $e->getMessage());
            }
        } else {
            custom_linking_debug_log("Method 1: stm_lms_add_user_course function not available");
        }

        // Method 2: Try using user meta approach (fallback)
        if (!$access_granted) {
            try {
                $user_courses = get_user_meta($user_id, 'stm_lms_course_id', false);
                if (!in_array($course_id, $user_courses)) {
                    $meta_result = add_user_meta($user_id, 'stm_lms_course_id', $course_id);
                    if ($meta_result) {
                        $access_granted = true;
                        custom_linking_debug_log("Method 2: Added course {$course_id} to user {$user_id} meta");
                    } else {
                        custom_linking_debug_log("Method 2: Failed to add course {$course_id} to user {$user_id} meta");
                    }
                } else {
                    custom_linking_debug_log("Method 2: User {$user_id} already has access to course {$course_id}");
                    $access_granted = true;
                }
            } catch (Exception $e) {
                custom_linking_debug_log("Method 2: Exception in user meta: " . $e->getMessage());
            }
        }

        // Method 3: Try using course meta approach (alternative)
        if (!$access_granted) {
            try {
                $course_students = get_post_meta($course_id, 'stm_lms_course_students', true);
                if (!is_array($course_students)) {
                    $course_students = array();
                }

                if (!in_array($user_id, $course_students)) {
                    $course_students[] = $user_id;
                    $meta_result = update_post_meta($course_id, 'stm_lms_course_students', $course_students);
                    if ($meta_result !== false) {
                        $access_granted = true;
                        custom_linking_debug_log("Method 3: Added user {$user_id} to course {$course_id} students");
                    } else {
                        custom_linking_debug_log("Method 3: Failed to add user {$user_id} to course {$course_id} students");
                    }
                } else {
                    custom_linking_debug_log("Method 3: User {$user_id} already in course {$course_id} students");
                    $access_granted = true;
                }
            } catch (Exception $e) {
                custom_linking_debug_log("Method 3: Exception in course meta: " . $e->getMessage());
            }
        }

        // Method 4: Try using WordPress user capabilities (another fallback)
        if (!$access_granted) {
            try {
                $user_obj = new WP_User($user_id);
                $capability = "access_course_{$course_id}";
                $cap_result = $user_obj->add_cap($capability);
                $access_granted = true;
                custom_linking_debug_log("Method 4: Added capability {$capability} to user {$user_id}");
            } catch (Exception $e) {
                custom_linking_debug_log("Method 4: Exception in user capabilities: " . $e->getMessage());
            }
        }

        // Log the final result
        if ($access_granted) {
            custom_linking_debug_log("Successfully granted access to course {$course_id} for user {$user_id} using available methods");

            // Fire a custom action for other plugins to hook into
            try {
                do_action('custom_linking_course_access_granted', $user_id, $course_id, $link_type);
            } catch (Exception $e) {
                custom_linking_debug_log("Exception in custom action: " . $e->getMessage());
            }
        } else {
            custom_linking_debug_log("Failed to grant access to course {$course_id} for user {$user_id}");
        }

        return $access_granted;

    } catch (Exception $e) {
        custom_linking_debug_log("Critical exception in custom_linking_grant_course_access: " . $e->getMessage());
        return false;
    }
}

// Initialize the order completion hooks
custom_linking_init_order_hooks();
