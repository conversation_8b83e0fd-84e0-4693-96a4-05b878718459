<?php
/**
 * Frontend functionality for Custom Linking Plugin
 *
 * This file handles the frontend integration with MasterStudy LMS and WooCommerce
 * It hooks into course display and modifies purchase/enrollment behavior
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize frontend hooks
 * 
 * This function sets up all the frontend hooks when the plugin is activated
 * It integrates with MasterStudy LMS course display and modifies the enrollment button
 */
function custom_linking_init_frontend() {
    /**
     * This block registers and enqueues our JavaScript for the frontend
     * We're loading our script only on course pages to avoid unnecessary overhead
     * The script intercepts the "Get Course" button clicks and handles redirection
     */
    add_action('wp_enqueue_scripts', 'custom_linking_enqueue_scripts');
    
    /**
     * These AJAX handlers process the course purchase requests
     * Both logged-in and non-logged-in users can trigger these actions
     * They find the linked WooCommerce product and add it to cart
     */
    add_action('wp_ajax_custom_linking_process_course', 'custom_linking_process_course');
    add_action('wp_ajax_nopriv_custom_linking_process_course', 'custom_linking_process_course');
    
    // Add debug info in footer when debugging is enabled
    add_action('wp_footer', 'custom_linking_debug_info', 100);
}

/**
 * Register and enqueue frontend scripts
 * 
 * This function loads our custom JavaScript file only on course pages
 * It handles the override of MasterStudy LMS "Get Course" button
 */
function custom_linking_enqueue_scripts() {
    /**
     * Only load on course pages to reduce overhead
     * The is_singular check confirms we're on a single course page
     */
    if (is_singular('stm-courses')) {
        wp_enqueue_script(
            'custom-linking-frontend',
            CUSTOM_LINKING_PLUGIN_URL . 'frontend/js/custom-linking.js',
            array('jquery'), // Dependency on jQuery
            CUSTOM_LINKING_PLUGIN_VERSION,
            true // Load in footer
        );
        
        /**
         * Pass data to our JavaScript file including AJAX URL and security nonce
         * This allows our JS to make secure AJAX calls to WordPress
         */
        wp_localize_script(
            'custom-linking-frontend',
            'customLinkingData',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('custom_linking_nonce')
            )
        );
    }
}

/**
 * Process course purchase AJAX request
 * 
 * This function handles the AJAX request when a user clicks the "Get Course" button
 * It finds linked products in our custom table and redirects to WooCommerce cart
 */
function custom_linking_process_course() {
    /**
     * Verify the security nonce to prevent CSRF attacks
     * This ensures the request came from our site and not a malicious source
     */
    check_ajax_referer('custom_linking_nonce', 'security');
    
    /**
     * Get and validate the course ID from the AJAX request
     * We use intval to ensure it's a valid integer value
     */
    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    if (!$course_id) {
        wp_send_json_error(array('error' => 'Invalid course ID'));
        return;
    }
    
    /**
     * Log the course ID for debugging purposes
     * This helps troubleshoot issues in the integration
     */
    custom_linking_debug_log('Processing course ID: ' . $course_id);
    
    /**
     * Get linked product from our custom table
     * We'll implement this function in the database module
     */
    $linked_product = custom_linking_get_linked_product($course_id);
    
    if ($linked_product) {
        /**
         * Product is linked, add to WooCommerce cart if WooCommerce is active
         * This redirects the user to the cart with the product added
         */
        if (function_exists('WC')) {
            // Add product to cart
            $added = WC()->cart->add_to_cart($linked_product);
            
            if ($added) {
                // Success! Send cart URL back to JS for redirection
                wp_send_json_success(array(
                    'linked_product' => true,
                    'product_id' => $linked_product,
                    'cart_url' => function_exists('wc_get_cart_url') ? wc_get_cart_url() : ''
                ));
            } else {
                // Failed to add to cart
                wp_send_json_error(array('error' => 'Failed to add product to cart'));
            }
        } else {
            // WooCommerce not active
            wp_send_json_error(array('error' => 'WooCommerce is not active'));
        }
    } else {
        /**
         * No linked product found, send response to JS
         * The JS will handle this case appropriately
         */
        wp_send_json_success(array(
            'linked_product' => false,
            'message' => 'No linked product found for this course'
        ));
    }
}

/**
 * Helper function to get linked product ID for a course
 * 
 * This is a temporary placeholder until we implement the full database integration
 * It will be replaced with actual database queries from our linking table
 *
 * @param int $course_id The course ID to find linked products for
 * @return int|false The linked product ID or false if none found
 */
function custom_linking_get_linked_product($course_id) {
    /**
     * For now, we're using the database function if it exists
     * Otherwise, we return false until the full implementation
     */
    if (function_exists('custom_linking_get_linked_product_id')) {
        return custom_linking_get_linked_product_id($course_id);
    }
    
    /**
     * Placeholder: Directly query the database
     * This provides a fallback if the database function doesn't exist
     */
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $product_id = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT product_id FROM $table_name WHERE course_id = %d LIMIT 1",
            $course_id
        )
    );
    
    return $product_id ? (int) $product_id : false;
}

/**
 * Display debug information in the footer when debugging is enabled
 * 
 * This function outputs plugin status information in the footer of the site
 * It only displays when WP_DEBUG is enabled
 */
function custom_linking_debug_info() {
    if (WP_DEBUG && is_user_logged_in() && current_user_can('manage_options')) {
        echo '<!-- Custom Linking Plugin is active -->';
    }
}

// Initialize the frontend functionality
custom_linking_init_frontend();