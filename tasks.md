# ✅ Custom Linking Plugin – Tasks

## 📊 Progress: 65% Complete

---

## 📁 Folder Responsibilities

- `admin/` → Handles all **admin-side UI**, settings, and panel logic  
- `database/` → Handles **plugin activation**, **table creation**, and all **DB queries**  
- `frontend/` → Manages **user flow**, button handling, cart redirection, etc.  
- `includes/` → Contains the **core logic**, acts as the **backbone** of the plugin  
  (business logic, processing, validation, structure, helpers, autoloader)

---

## 🧩 Task 1: Admin Panel (`/admin`) - ✅ COMPLETED
- [x] Task 1.1: Initialize admin menu and settings page
- [x] Task 1.2: Create `adminLink.php` and link it to the main plugin file
- [x] Task 1.3: Create UI for entering LMS Course ID + WooCommerce Product ID
- [x] Task 1.4: Add dropdown for selecting link type (`course` or `bundle`)
- [x] Task 1.5: Pass data to `includes/` for processing and DB insertion
- [x] Task 1.6: Show existing links with option to edit/delete

---

## 🗃️ Task 2: Database (`/database`) - ✅ COMPLETED
- [x] Task 2.1: Create `databaseLink.php` and link it to the main plugin file
- [x] Task 2.2: Create table on plugin activation (course_id, product_id, type)
- [x] Task 2.3: Create plugin uninstall function to remove table if needed
- [x] Task 2.4: Add DB functions for insert, update, delete, get links

---

## 🌐 Task 3: Frontend Flow (`/frontend`)
- [x] Task 3.1: Create `frontendLink.php` and link it to main plugin file
- [x] Task 3.2: Create `/frontend/js/custom-linking.js` to override LMS button functionality
- [x] Task 3.3: Modify copied LMS JavaScript to intercept the "Get Course" button click
- [ ] Task 3.4: Create AJAX handler to process course ID and find linked products
- [x] Task 3.5: Register and enqueue scripts with proper dependencies to ensure load order
- [ ] Task 3.6: Add WooCommerce cart integration to add linked product to cart
- [x] Task 3.7: Implement redirect functionality (currently redirecting to Google as a test)
- [ ] Task 3.8: Handle both logged-in and guest user scenarios
- [ ] Task 3.9: Add fallback behavior when no linked product exists

---

## 🧠 Task 4: Includes / Core Logic (`/includes`) - ✅ COMPLETED
- [x] Task 4.1: Create `includesLink.php` and connect it in the main plugin file
- [x] Task 4.2: Create `class-core.php` (acts as autoloader + plugin backbone)
- [x] Task 4.3: Handle logic for saving, retrieving, validating link data
- [x] Task 4.4: Create reusable functions (e.g., `get_linked_product($course_id)`)
- [x] Task 4.5: Create utility functions (e.g., `log_event()`, `sanitize_input()`)
- [x] Task 4.6: Keep all logic separated from UI for clean MVC-ish structure

---

## 🛒 Task 5: WooCommerce Purchase Access Logic - 🟡 IN PROGRESS
- [ ] Task 5.1: Create a function to hook into `woocommerce_order_status_completed` action
- [ ] Task 5.2: Extract product IDs from completed orders
- [ ] Task 5.3: Query our custom table to find linked courses for each product
- [ ] Task 5.4: Use MasterStudy LMS functions to grant course access to the purchasing user
- [ ] Task 5.5: Handle different link types (`course` vs `bundle`)
- [ ] Task 5.6: Add proper error handling and logging for troubleshooting
- [ ] Task 5.7: Create test cases to verify course access is granted after purchase

---

## 🧰 Task 6: System Utilities - ✅ COMPLETED
- [x] Task 6.1: Create uninstall hook for plugin cleanup (optional)
- [x] Task 6.2: Create debug logger function (logs to `debug.log`)
- [x] Task 6.3: Add `WP_DEBUG` constants in config if not already present

---

## 📄 Task 7: Documentation - 🟡 IN PROGRESS
- [x] Task 7.1: Finalize and maintain `instructions.md`
- [x] Task 7.2: Track and check off items in `tasks.md`
- [x] Task 7.3: Document admin usage (partially complete in code comments)
- [ ] Task 7.4: Document frontend integration details
- [ ] Task 7.5: Document WooCommerce purchase flow

---

## 🧪 Task 8: Testing
- [ ] Task 8.1: Create temp test files (e.g., `test-db.php`) to test logic in isolation
- [ ] Task 8.2: Test admin panel (add/edit/delete links)
- [ ] Task 8.3: Test frontend flow (course → cart → checkout)
- [ ] Task 8.4: Test course access logic after purchase
- [ ] Task 8.5: Handle edge cases and error logs

---

## ✅ Completed Tasks
- Database setup and table creation
- Plugin activation/deactivation hooks
- Core plugin structure
- Admin interface for managing course-product links
- Basic utility functions and debug logging
- Basic documentation

## 🚧 Next Steps
1. ✅ Successfully implemented button override and tested redirection
2. Create AJAX handler to fetch linked WooCommerce products for each course ID
3. Replace Google redirect with WooCommerce cart integration
4. Implement WooCommerce order completion hooks to grant course access
5. Add error handling and fallback behavior when no linked products exist
6. Test the complete purchase flow from course to product to access
