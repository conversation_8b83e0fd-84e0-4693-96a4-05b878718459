<?php
/**
 * Course Linking Handler for Custom Linking Plugin
 * 
 * This file handles AJAX requests to query course-product links
 * and logs the results to debug.log
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Handle AJAX request for course linking
 * 
 * This function processes the course ID, queries the database for linked products,
 * and logs the result to debug.log
 */
function custom_linking_handle_course_request() {
    // Clear previous debug messages
    custom_linking_clear_debug_log();
    
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'custom_linking_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        return;
    }
    
    // Get course ID from POST data
    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    
    if (!$course_id) {
        custom_linking_debug_log('Error: No course ID provided');
        wp_send_json_error(array('message' => 'No course ID provided'));
        return;
    }
    
    // Query the database for linked product
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $product_id = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT product_id FROM $table_name WHERE course_id = %d LIMIT 1",
            $course_id
        )
    );
    
    if ($product_id) {
        // Log the successful link
        $message = "Course ID {$course_id} linked to Product ID {$product_id}";
        custom_linking_debug_log($message);
        
        wp_send_json_success(array(
            'message' => $message,
            'course_id' => $course_id,
            'product_id' => $product_id
        ));
    } else {
        // Log that no link was found
        $message = "Course ID {$course_id} has no linked product";
        custom_linking_debug_log($message);
        
        wp_send_json_success(array(
            'message' => $message,
            'course_id' => $course_id,
            'product_id' => null
        ));
    }
}

// Register AJAX handlers
add_action('wp_ajax_custom_linking_course_request', 'custom_linking_handle_course_request');
add_action('wp_ajax_nopriv_custom_linking_course_request', 'custom_linking_handle_course_request');
