[2025-06-05 12:21:44] Plugin: Debug utilities loaded
[2025-06-05 12:21:44] Plugin: Core includes loaded
[2025-06-05 12:21:44] Plugin: Database files loaded
[2025-06-05 12:21:44] Plugin: Admin files loaded
[2025-06-05 12:21:44] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:21:44] Plugin: Frontend files loaded
[2025-06-05 12:21:44] Plugin: Core class loaded
[2025-06-05 12:21:44] Plugin: About to create instance
[2025-06-05 12:21:44] Core: Constructor called
[2025-06-05 12:21:44] Core: init_plugin called
[2025-06-05 12:21:44] Core: About to call init_components
[2025-06-05 12:21:44] Core: Initializing plugin components
[2025-06-05 12:21:44] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:21:44] Core: init_components completed
[2025-06-05 12:21:44] Core: Constructor completed
[2025-06-05 12:21:45] Plugin: Debug utilities loaded
[2025-06-05 12:21:45] Plugin: Core includes loaded
[2025-06-05 12:21:45] Plugin: Database files loaded
[2025-06-05 12:21:45] Plugin: Admin files loaded
[2025-06-05 12:21:45] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:21:45] Plugin: Frontend files loaded
[2025-06-05 12:21:45] Plugin: Core class loaded
[2025-06-05 12:21:45] Plugin: About to create instance
[2025-06-05 12:21:45] Core: Constructor called
[2025-06-05 12:21:45] Core: init_plugin called
[2025-06-05 12:21:45] Core: About to call init_components
[2025-06-05 12:21:45] Core: Initializing plugin components
[2025-06-05 12:21:45] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:21:45] Core: init_components completed
[2025-06-05 12:21:45] Core: Constructor completed
[2025-06-05 12:21:52] Plugin: Debug utilities loaded
[2025-06-05 12:21:52] Plugin: Core includes loaded
[2025-06-05 12:21:52] Plugin: Database files loaded
[2025-06-05 12:21:52] Plugin: Admin files loaded
[2025-06-05 12:21:52] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:21:52] Plugin: Frontend files loaded
[2025-06-05 12:21:52] Plugin: Core class loaded
[2025-06-05 12:21:52] Plugin: About to create instance
[2025-06-05 12:21:52] Core: Constructor called
[2025-06-05 12:21:52] Core: init_plugin called
[2025-06-05 12:21:52] Core: About to call init_components
[2025-06-05 12:21:52] Core: Initializing plugin components
[2025-06-05 12:21:52] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:21:52] Core: init_components completed
[2025-06-05 12:21:52] Core: Constructor completed
[2025-06-05 12:21:53] Custom Linking JS enqueued on course page
[2025-06-05 12:21:55] Plugin: Debug utilities loaded
[2025-06-05 12:21:55] Plugin: Core includes loaded
[2025-06-05 12:21:55] Plugin: Database files loaded
[2025-06-05 12:21:55] Plugin: Admin files loaded
[2025-06-05 12:21:55] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:21:55] Plugin: Frontend files loaded
[2025-06-05 12:21:55] Plugin: Core class loaded
[2025-06-05 12:21:55] Plugin: About to create instance
[2025-06-05 12:21:55] Core: Constructor called
[2025-06-05 12:21:55] Core: init_plugin called
[2025-06-05 12:21:55] Core: About to call init_components
[2025-06-05 12:21:55] Core: Initializing plugin components
[2025-06-05 12:21:55] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:21:55] Core: init_components completed
[2025-06-05 12:21:55] Core: Constructor completed
[2025-06-05 12:21:55] Plugin: Debug utilities loaded
[2025-06-05 12:21:55] Plugin: Core includes loaded
[2025-06-05 12:21:55] Plugin: Database files loaded
[2025-06-05 12:21:55] Plugin: Admin files loaded
[2025-06-05 12:21:55] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:21:55] Plugin: Frontend files loaded
[2025-06-05 12:21:55] Plugin: Core class loaded
[2025-06-05 12:21:55] Plugin: About to create instance
[2025-06-05 12:21:55] Core: Constructor called
[2025-06-05 12:21:55] Core: init_plugin called
[2025-06-05 12:21:55] Core: About to call init_components
[2025-06-05 12:21:55] Core: Initializing plugin components
[2025-06-05 12:21:55] Plugin: Debug utilities loaded
[2025-06-05 12:21:55] Core: Setting up admin hooks
[2025-06-05 12:21:55] Core: Admin menu hook registered
[2025-06-05 12:21:55] Plugin: Core includes loaded
[2025-06-05 12:21:55] Core: Admin scripts hook registered
[2025-06-05 12:21:55] Core: init_components completed
[2025-06-05 12:21:55] Plugin: Database files loaded
[2025-06-05 12:21:55] Core: Constructor completed
[2025-06-05 12:21:55] Plugin: Admin files loaded
[2025-06-05 12:21:55] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:21:55] Plugin: Frontend files loaded
[2025-06-05 12:21:55] Plugin: Core class loaded
[2025-06-05 12:21:55] Plugin: About to create instance
[2025-06-05 12:21:55] Core: Constructor called
[2025-06-05 12:21:55] Core: init_plugin called
[2025-06-05 12:21:55] Core: About to call init_components
[2025-06-05 12:21:55] Core: Initializing plugin components
[2025-06-05 12:21:55] Core: Setting up admin hooks
[2025-06-05 12:21:55] Core: Admin menu hook registered
[2025-06-05 12:21:55] Core: Admin scripts hook registered
[2025-06-05 12:21:55] Core: init_components completed
[2025-06-05 12:21:55] Core: Constructor completed
[2025-06-05 12:22:04] Plugin: Debug utilities loaded
[2025-06-05 12:22:04] Plugin: Core includes loaded
[2025-06-05 12:22:04] Plugin: Debug utilities loaded
[2025-06-05 12:22:04] Plugin: Core includes loaded
[2025-06-05 12:22:04] Plugin: Database files loaded
[2025-06-05 12:22:04] Plugin: Database files loaded
[2025-06-05 12:22:04] Plugin: Admin files loaded
[2025-06-05 12:22:04] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:22:04] Plugin: Admin files loaded
[2025-06-05 12:22:04] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:22:04] Plugin: Frontend files loaded
[2025-06-05 12:22:04] Plugin: Frontend files loaded
[2025-06-05 12:22:04] Plugin: Core class loaded
[2025-06-05 12:22:04] Plugin: About to create instance
[2025-06-05 12:22:04] Plugin: Core class loaded
[2025-06-05 12:22:04] Core: Constructor called
[2025-06-05 12:22:04] Plugin: About to create instance
[2025-06-05 12:22:04] Core: init_plugin called
[2025-06-05 12:22:04] Core: Constructor called
[2025-06-05 12:22:04] Core: About to call init_components
[2025-06-05 12:22:04] Core: init_plugin called
[2025-06-05 12:22:04] Core: Initializing plugin components
[2025-06-05 12:22:04] Core: About to call init_components
[2025-06-05 12:22:04] Core: Setting up admin hooks
[2025-06-05 12:22:04] Core: Admin menu hook registered
[2025-06-05 12:22:04] Core: Initializing plugin components
[2025-06-05 12:22:04] Core: Setting up admin hooks
[2025-06-05 12:22:04] Core: Admin scripts hook registered
[2025-06-05 12:22:04] Core: Admin menu hook registered
[2025-06-05 12:22:04] Core: init_components completed
[2025-06-05 12:22:04] Core: Admin scripts hook registered
[2025-06-05 12:22:04] Core: Constructor completed
[2025-06-05 12:22:04] Core: init_components completed
[2025-06-05 12:22:04] Core: Constructor completed
[2025-06-05 12:22:06] Querying the table for course ID: 49599
[2025-06-05 12:22:06] Found the linked product ID
[2025-06-05 12:22:06] Product ID is 1234 and course ID is 49599
[2025-06-05 12:22:06] Plugin: Debug utilities loaded
[2025-06-05 12:22:06] Plugin: Core includes loaded
[2025-06-05 12:22:06] Plugin: Database files loaded
[2025-06-05 12:22:06] Plugin: Admin files loaded
[2025-06-05 12:22:06] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:22:06] Plugin: Frontend files loaded
[2025-06-05 12:22:06] Plugin: Core class loaded
[2025-06-05 12:22:06] Plugin: About to create instance
[2025-06-05 12:22:06] Core: Constructor called
[2025-06-05 12:22:06] Core: init_plugin called
[2025-06-05 12:22:06] Core: About to call init_components
[2025-06-05 12:22:06] Core: Initializing plugin components
[2025-06-05 12:22:06] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:22:06] Core: init_components completed
[2025-06-05 12:22:06] Core: Constructor completed
[2025-06-05 12:22:09] Plugin: Debug utilities loaded
[2025-06-05 12:22:09] Plugin: Core includes loaded
[2025-06-05 12:22:09] Plugin: Database files loaded
[2025-06-05 12:22:09] Plugin: Admin files loaded
[2025-06-05 12:22:09] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:22:09] Plugin: Frontend files loaded
[2025-06-05 12:22:09] Plugin: Core class loaded
[2025-06-05 12:22:09] Plugin: About to create instance
[2025-06-05 12:22:09] Core: Constructor called
[2025-06-05 12:22:09] Core: init_plugin called
[2025-06-05 12:22:09] Core: About to call init_components
[2025-06-05 12:22:09] Core: Initializing plugin components
[2025-06-05 12:22:09] Core: Setting up admin hooks
[2025-06-05 12:22:09] Core: Admin menu hook registered
[2025-06-05 12:22:09] Core: Admin scripts hook registered
[2025-06-05 12:22:09] Core: init_components completed
[2025-06-05 12:22:09] Core: Constructor completed
[2025-06-05 12:42:18] Plugin: Debug utilities loaded
[2025-06-05 12:42:18] Plugin: Core includes loaded
[2025-06-05 12:42:18] Plugin: Database files loaded
[2025-06-05 12:42:18] Plugin: Admin files loaded
[2025-06-05 12:42:18] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:42:18] Plugin: Frontend files loaded
[2025-06-05 12:42:18] Plugin: Core class loaded
[2025-06-05 12:42:18] Plugin: About to create instance
[2025-06-05 12:42:18] Core: Constructor called
[2025-06-05 12:42:18] Core: init_plugin called
[2025-06-05 12:42:18] Core: About to call init_components
[2025-06-05 12:42:18] Core: Initializing plugin components
[2025-06-05 12:42:18] Core: Setting up admin hooks
[2025-06-05 12:42:18] Core: Admin menu hook registered
[2025-06-05 12:42:18] Core: Admin scripts hook registered
[2025-06-05 12:42:18] Core: init_components completed
[2025-06-05 12:42:18] Core: Constructor completed
[2025-06-05 12:42:19] Plugin: Debug utilities loaded
[2025-06-05 12:42:19] Plugin: Core includes loaded
[2025-06-05 12:42:19] Plugin: Database files loaded
[2025-06-05 12:42:19] Plugin: Admin files loaded
[2025-06-05 12:42:19] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:42:19] Plugin: Frontend files loaded
[2025-06-05 12:42:19] Plugin: Core class loaded
[2025-06-05 12:42:19] Plugin: About to create instance
[2025-06-05 12:42:19] Core: Constructor called
[2025-06-05 12:42:19] Core: init_plugin called
[2025-06-05 12:42:19] Core: About to call init_components
[2025-06-05 12:42:19] Core: Initializing plugin components
[2025-06-05 12:42:19] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:42:19] Core: init_components completed
[2025-06-05 12:42:19] Core: Constructor completed
[2025-06-05 12:42:21] Plugin: Debug utilities loaded
[2025-06-05 12:42:21] Plugin: Core includes loaded
[2025-06-05 12:42:21] Plugin: Database files loaded
[2025-06-05 12:42:21] Plugin: Admin files loaded
[2025-06-05 12:42:21] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:42:21] Plugin: Frontend files loaded
[2025-06-05 12:42:21] Plugin: Core class loaded
[2025-06-05 12:42:21] Plugin: About to create instance
[2025-06-05 12:42:21] Core: Constructor called
[2025-06-05 12:42:21] Core: init_plugin called
[2025-06-05 12:42:21] Core: About to call init_components
[2025-06-05 12:42:21] Core: Initializing plugin components
[2025-06-05 12:42:21] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:42:21] Core: init_components completed
[2025-06-05 12:42:21] Core: Constructor completed
[2025-06-05 12:43:18] Plugin: Debug utilities loaded
[2025-06-05 12:43:18] Plugin: Core includes loaded
[2025-06-05 12:43:18] Plugin: Database files loaded
[2025-06-05 12:43:18] Plugin: Admin files loaded
[2025-06-05 12:43:18] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:43:18] Plugin: Frontend files loaded
[2025-06-05 12:43:18] Plugin: Core class loaded
[2025-06-05 12:43:18] Plugin: About to create instance
[2025-06-05 12:43:18] Core: Constructor called
[2025-06-05 12:43:18] Core: init_plugin called
[2025-06-05 12:43:18] Core: About to call init_components
[2025-06-05 12:43:18] Core: Initializing plugin components
[2025-06-05 12:43:18] Core: Setting up admin hooks
[2025-06-05 12:43:18] Core: Admin menu hook registered
[2025-06-05 12:43:18] Core: Admin scripts hook registered
[2025-06-05 12:43:18] Core: init_components completed
[2025-06-05 12:43:18] Core: Constructor completed
[2025-06-05 12:43:19] Plugin: Debug utilities loaded
[2025-06-05 12:43:19] Plugin: Core includes loaded
[2025-06-05 12:43:19] Plugin: Database files loaded
[2025-06-05 12:43:19] Plugin: Admin files loaded
[2025-06-05 12:43:19] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:43:19] Plugin: Frontend files loaded
[2025-06-05 12:43:19] Plugin: Core class loaded
[2025-06-05 12:43:19] Plugin: About to create instance
[2025-06-05 12:43:19] Core: Constructor called
[2025-06-05 12:43:19] Core: init_plugin called
[2025-06-05 12:43:19] Core: About to call init_components
[2025-06-05 12:43:19] Core: Initializing plugin components
[2025-06-05 12:43:19] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:43:19] Core: init_components completed
[2025-06-05 12:43:19] Core: Constructor completed
[2025-06-05 12:45:19] Plugin: Debug utilities loaded
[2025-06-05 12:45:19] Plugin: Core includes loaded
[2025-06-05 12:45:19] Plugin: Database files loaded
[2025-06-05 12:45:19] Plugin: Admin files loaded
[2025-06-05 12:45:19] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:45:19] Plugin: Frontend files loaded
[2025-06-05 12:45:19] Plugin: Core class loaded
[2025-06-05 12:45:19] Plugin: About to create instance
[2025-06-05 12:45:19] Core: Constructor called
[2025-06-05 12:45:19] Core: init_plugin called
[2025-06-05 12:45:19] Core: About to call init_components
[2025-06-05 12:45:19] Core: Initializing plugin components
[2025-06-05 12:45:19] Core: Setting up admin hooks
[2025-06-05 12:45:19] Core: Admin menu hook registered
[2025-06-05 12:45:19] Core: Admin scripts hook registered
[2025-06-05 12:45:19] Core: init_components completed
[2025-06-05 12:45:19] Core: Constructor completed
[2025-06-05 12:45:20] Plugin: Debug utilities loaded
[2025-06-05 12:45:20] Plugin: Core includes loaded
[2025-06-05 12:45:20] Plugin: Database files loaded
[2025-06-05 12:45:20] Plugin: Admin files loaded
[2025-06-05 12:45:20] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:45:20] Plugin: Frontend files loaded
[2025-06-05 12:45:20] Plugin: Core class loaded
[2025-06-05 12:45:20] Plugin: About to create instance
[2025-06-05 12:45:20] Core: Constructor called
[2025-06-05 12:45:20] Core: init_plugin called
[2025-06-05 12:45:20] Core: About to call init_components
[2025-06-05 12:45:20] Core: Initializing plugin components
[2025-06-05 12:45:20] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:45:20] Core: init_components completed
[2025-06-05 12:45:20] Core: Constructor completed
[2025-06-05 12:47:20] Plugin: Debug utilities loaded
[2025-06-05 12:47:20] Plugin: Core includes loaded
[2025-06-05 12:47:20] Plugin: Database files loaded
[2025-06-05 12:47:20] Plugin: Admin files loaded
[2025-06-05 12:47:20] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:47:20] Plugin: Frontend files loaded
[2025-06-05 12:47:20] Plugin: Core class loaded
[2025-06-05 12:47:20] Plugin: About to create instance
[2025-06-05 12:47:20] Core: Constructor called
[2025-06-05 12:47:20] Core: init_plugin called
[2025-06-05 12:47:20] Core: About to call init_components
[2025-06-05 12:47:20] Core: Initializing plugin components
[2025-06-05 12:47:20] Core: Setting up admin hooks
[2025-06-05 12:47:20] Core: Admin menu hook registered
[2025-06-05 12:47:20] Core: Admin scripts hook registered
[2025-06-05 12:47:20] Core: init_components completed
[2025-06-05 12:47:20] Core: Constructor completed
[2025-06-05 12:47:21] Plugin: Debug utilities loaded
[2025-06-05 12:47:21] Plugin: Core includes loaded
[2025-06-05 12:47:21] Plugin: Database files loaded
[2025-06-05 12:47:21] Plugin: Admin files loaded
[2025-06-05 12:47:21] Plugin: custom_linking_admin_menu function found
[2025-06-05 12:47:21] Plugin: Frontend files loaded
[2025-06-05 12:47:21] Plugin: Core class loaded
[2025-06-05 12:47:21] Plugin: About to create instance
[2025-06-05 12:47:21] Core: Constructor called
[2025-06-05 12:47:21] Core: init_plugin called
[2025-06-05 12:47:21] Core: About to call init_components
[2025-06-05 12:47:21] Core: Initializing plugin components
[2025-06-05 12:47:21] Core: Not in admin area, skipping admin hooks
[2025-06-05 12:47:21] Core: init_components completed
[2025-06-05 12:47:21] Core: Constructor completed
